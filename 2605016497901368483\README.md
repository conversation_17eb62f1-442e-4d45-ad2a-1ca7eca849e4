# 清代西域诗集注文档分析工具

这个工具用于分析《清代西域诗集注》文档，统计"乌鲁木齐"、"伊犁"、"哈密"三个词语的出现次数，并为每个词语生成词云图。

## 📊 分析结果

根据对《清代西域诗辑注.docx》的分析：

- **乌鲁木齐**: 445次 (36.4%)
- **伊犁**: 565次 (46.2%)
- **哈密**: 213次 (17.4%)
- **总计**: 1,223次

## 🚀 使用方法

### 1. 安装依赖
```bash
pip install python-docx docx2txt wordcloud matplotlib jieba
```

### 2. 运行分析程序
```bash
python document_analysis.py
```

### 3. 查看结果
程序会在 `results/` 目录中生成：
- `analysis_results.txt` - 统计结果
- `乌鲁木齐_wordcloud.png` - 乌鲁木齐词云图
- `伊犁_wordcloud.png` - 伊犁词云图
- `哈密_wordcloud.png` - 哈密词云图

## 📁 文件说明

- `document_analysis.py` - 主要分析程序（推荐使用）
- `simple_wordcloud.py` - 简化版分析程序（备用）
- `README.md` - 使用说明文档

## 🔧 技术特点

- **文档解析**: 支持 .doc 和 .docx 格式
- **中文分词**: 使用jieba库进行智能分词
- **词云生成**: 使用WordCloud库生成可视化图片
- **中文字体**: 自动配置中文字体支持

## 💡 故障排除

如果遇到依赖包安装问题，可以使用简化版：
```bash
python simple_wordcloud.py
```

## 📈 数据洞察

- 伊犁出现频率最高，体现其在清代西域的重要地位
- 乌鲁木齐作为政治中心，在诗集中占据重要位置
- 哈密作为进疆门户，虽然出现次数相对较少但意义重大
