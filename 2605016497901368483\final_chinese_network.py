import matplotlib.pyplot as plt
import networkx as nx
import matplotlib.patches as mpatches
import numpy as np

# 设置中文字体 - 使用最简单的方法
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

def create_network():
    """创建社交网络"""
    G = nx.Graph()
    
    # 人物信息
    characters = {
        '颜检': {'type': 'core', 'position': (0, 0), 'size': 5000, 'label': '颜检\n(岱云)'},
        '成林': {'type': 'close_friend', 'position': (-3, 2.5), 'size': 3500, 'label': '成林\n(漪园)'},
        '遇昌': {'type': 'close_friend', 'position': (3, 2.5), 'size': 3500, 'label': '遇昌\n(晓亭)'},
        '贡楚克札布': {'type': 'close_friend', 'position': (-3, -2.5), 'size': 3500, 'label': '贡楚克札布\n(果斋)'},
        '李銮宣': {'type': 'close_friend', 'position': (3, -2.5), 'size': 3500, 'label': '李銮宣\n(石农)'},
        '晋昌': {'type': 'colleague', 'position': (-5.5, 0), 'size': 2800, 'label': '晋昌'},
        '莫子捷': {'type': 'fellow_townsman', 'position': (5.5, 0), 'size': 2800, 'label': '莫子捷\n(远崖)'}
    }
    
    # 添加节点
    for name, info in characters.items():
        G.add_node(name, **info)
    
    # 关系定义
    relationships = [
        ('颜检', '成林', {'weight': 4, 'relation': '密友'}),
        ('颜检', '遇昌', {'weight': 4, 'relation': '密友'}),
        ('颜检', '贡楚克札布', {'weight': 4, 'relation': '密友'}),
        ('颜检', '李銮宣', {'weight': 4, 'relation': '密友'}),
        ('颜检', '晋昌', {'weight': 2, 'relation': '诗友'}),
        ('颜检', '莫子捷', {'weight': 1, 'relation': '同乡'}),
        ('成林', '遇昌', {'weight': 2, 'relation': '同游'}),
        ('遇昌', '贡楚克札布', {'weight': 2, 'relation': '同游'}),
        ('贡楚克札布', '李銮宣', {'weight': 2, 'relation': '同游'}),
    ]
    
    # 添加边
    for source, target, attrs in relationships:
        G.add_edge(source, target, **attrs)
    
    return G, characters

def draw_network(G, characters):
    """绘制网络图"""
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    fig.patch.set_facecolor('#f8f9fa')
    ax.set_facecolor('#f8f9fa')
    
    # 配色方案
    node_colors = {
        'core': '#e74c3c',           # 红色
        'close_friend': '#3498db',   # 蓝色
        'colleague': '#9b59b6',      # 紫色
        'fellow_townsman': '#27ae60' # 绿色
    }
    
    # 获取位置
    pos = {name: info['position'] for name, info in characters.items()}
    
    # 绘制边
    for edge in G.edges(data=True):
        source, target, data = edge
        weight = data['weight']
        
        # 根据关系类型设置颜色
        if data['relation'] == '密友':
            color = '#e74c3c'
        elif data['relation'] == '诗友':
            color = '#3498db'
        elif data['relation'] == '同乡':
            color = '#27ae60'
        else:
            color = '#95a5a6'
        
        nx.draw_networkx_edges(G, pos, 
                              edgelist=[(source, target)],
                              width=weight * 2,
                              alpha=0.7, 
                              edge_color=color)
    
    # 绘制节点
    for node_name, node_data in characters.items():
        node_type = node_data['type']
        color = node_colors[node_type]
        size = node_data['size']
        
        nx.draw_networkx_nodes(G, pos, 
                             nodelist=[node_name],
                             node_color=color,
                             node_size=size,
                             alpha=0.9,
                             edgecolors='white',
                             linewidths=3)
    
    # 使用简单的方法添加中文标签
    for name, data in characters.items():
        x, y = pos[name]
        label = data['label']
        
        # 根据节点类型调整字体大小
        if data['type'] == 'core':
            fontsize = 14
        else:
            fontsize = 11
        
        # 直接在节点上绘制文字
        ax.annotate(label, 
                   xy=(x, y), 
                   xytext=(0, 0),
                   textcoords='offset points',
                   ha='center', 
                   va='center',
                   fontsize=fontsize,
                   fontweight='bold',
                   color='white',
                   bbox=dict(boxstyle="round,pad=0.3", 
                            facecolor='black', 
                            alpha=0.6,
                            edgecolor='none'))
    
    # 创建图例
    legend_elements = [
        mpatches.Patch(color=node_colors['core'], label='核心人物', alpha=0.9),
        mpatches.Patch(color=node_colors['close_friend'], label='一级核心圈（谪戍挚友）', alpha=0.9),
        mpatches.Patch(color=node_colors['colleague'], label='二级关系圈（诗友同僚）', alpha=0.9),
        mpatches.Patch(color=node_colors['fellow_townsman'], label='同乡关系', alpha=0.9)
    ]
    
    ax.legend(handles=legend_elements, 
             loc='upper left', 
             bbox_to_anchor=(0.02, 0.98),
             frameon=True,
             fancybox=True,
             shadow=True,
             fontsize=13,
             title='人物关系类型',
             title_fontsize=14)
    
    # 标题
    ax.text(0.5, 0.95, "颜检人物社会关系网络图", 
           transform=ax.transAxes,
           fontsize=22, 
           fontweight='bold',
           ha='center',
           color='#2c3e50')
    
    ax.text(0.5, 0.91, '嘉庆十一年至十三年 乌鲁木齐谪戍期间 (1807-1808)', 
           transform=ax.transAxes,
           fontsize=14, 
           ha='center',
           color='#7f8c8d')
    
    # 移除坐标轴
    ax.set_axis_off()
    
    # 设置边界
    ax.set_xlim(-7, 7)
    ax.set_ylim(-4, 4)
    
    plt.tight_layout()
    return fig

def main():
    """主函数"""
    print("正在创建颜检人物社会关系网络图...")
    
    # 创建网络
    G, characters = create_network()
    
    # 绘制图形
    fig = draw_network(G, characters)
    
    # 保存图片
    output_file = '颜检人物社会关系网络图_最终版.png'
    
    # 使用不同的保存方法，避免字体问题
    try:
        plt.savefig(output_file, dpi=300, bbox_inches='tight', 
                    facecolor='white', edgecolor='none',
                    format='png')
        print(f"✅ 最终版网络图已保存为: {output_file}")
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        # 备用保存方法
        plt.savefig('social_network_backup.png', dpi=150, 
                    bbox_inches='tight', facecolor='white')
        print("✅ 备用版本已保存为: social_network_backup.png")
    
    # 输出网络统计信息
    print(f"\n📊 网络统计信息:")
    print(f"节点数量: {G.number_of_nodes()}")
    print(f"边数量: {G.number_of_edges()}")
    print(f"网络密度: {nx.density(G):.3f}")
    
    # 输出关系信息
    print("\n🔗 人物关系详情:")
    for edge in G.edges(data=True):
        source, target, data = edge
        print(f"• {source} ↔ {target}: {data['relation']}")
    
    print("\n🏛️ 历史背景:")
    print("• 核心活动: 诗文唱和、同游名胜、宴集聚会、相互慰藉")
    print("• 主要地点: 乌鲁木齐、红山、大石头山寺、迪化城")
    print("• 时间背景: 嘉庆十一年至十三年（1807-1808年）")
    print("• 历史意义: 展现了清代谪戍文人的社交网络和精神慰藉")

if __name__ == "__main__":
    main()
