#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清代西域诗集注文档分析工具
功能：
1. 从Word文档中提取文本
2. 统计指定词语出现次数
3. 生成词云图
"""

import os
import re
from collections import defaultdict
import docx
import docx2txt
from wordcloud import WordCloud
import matplotlib.pyplot as plt
import jieba
import json

class DocumentAnalyzer:
    def __init__(self):
        self.target_words = ["乌鲁木齐", "伊犁", "哈密"]
        self.document_text = ""
        self.word_counts = {}
        self.context_data = defaultdict(list)
        
        # 创建结果目录
        self.results_dir = "results"
        if not os.path.exists(self.results_dir):
            os.makedirs(self.results_dir)
    
    def extract_text_from_docx(self, file_path):
        """从.docx文件提取文本"""
        try:
            doc = docx.Document(file_path)
            text = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text.append(paragraph.text.strip())
            return "\n".join(text)
        except Exception as e:
            print(f"读取.docx文件失败: {e}")
            return ""
    
    def extract_text_from_doc(self, file_path):
        """从.doc文件提取文本"""
        try:
            text = docx2txt.process(file_path)
            return text if text else ""
        except Exception as e:
            print(f"读取.doc文件失败: {e}")
            return ""
    
    def load_document(self, file_path):
        """加载文档并提取文本"""
        print(f"正在加载文档: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"文件不存在: {file_path}")
            return False
        
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext == '.docx':
            self.document_text = self.extract_text_from_docx(file_path)
        elif file_ext == '.doc':
            self.document_text = self.extract_text_from_doc(file_path)
        else:
            print(f"不支持的文件格式: {file_ext}")
            return False
        
        if not self.document_text:
            print("未能提取到文档内容")
            return False
        
        print(f"成功提取文档内容，总字符数: {len(self.document_text)}")
        return True
    
    def count_target_words(self):
        """统计目标词语出现次数"""
        print("正在统计词语出现次数...")
        
        for word in self.target_words:
            # 使用正则表达式精确匹配
            pattern = re.compile(word)
            matches = pattern.findall(self.document_text)
            self.word_counts[word] = len(matches)
            print(f"'{word}' 出现次数: {self.word_counts[word]}")
        
        total_count = sum(self.word_counts.values())
        print(f"三个词语总出现次数: {total_count}")
        
        return self.word_counts
    
    def extract_context(self, word, context_length=50):
        """提取词语的上下文"""
        contexts = []
        text = self.document_text
        
        # 找到所有出现位置
        for match in re.finditer(word, text):
            start = max(0, match.start() - context_length)
            end = min(len(text), match.end() + context_length)
            context = text[start:end]
            contexts.append(context)
        
        return contexts
    
    def prepare_wordcloud_text(self, word):
        """为词云准备文本数据"""
        print(f"正在为 '{word}' 准备词云文本...")
        
        # 提取上下文
        contexts = self.extract_context(word, context_length=100)
        
        if not contexts:
            print(f"未找到 '{word}' 的上下文")
            return ""
        
        # 合并所有上下文
        combined_text = " ".join(contexts)
        
        # 使用jieba分词
        words = jieba.cut(combined_text)
        word_list = []
        
        # 过滤停用词和标点符号
        stop_words = set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'])
        
        for w in words:
            w = w.strip()
            if len(w) > 1 and w not in stop_words and not re.match(r'^[0-9\s\W]+$', w):
                word_list.append(w)
        
        return " ".join(word_list)
    
    def generate_wordcloud(self, word, text_data):
        """生成词云图"""
        if not text_data:
            print(f"没有足够的文本数据生成 '{word}' 的词云")
            return

        print(f"正在生成 '{word}' 的词云图...")

        try:
            # 设置matplotlib中文字体支持
            import matplotlib
            matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
            matplotlib.rcParams['axes.unicode_minus'] = False

            # 配置词云参数
            wordcloud = WordCloud(
                width=800,
                height=600,
                background_color='white',
                max_words=100,
                font_path='C:/Windows/Fonts/simhei.ttf',  # Windows系统黑体字体
                colormap='viridis',
                relative_scaling=0.5,
                random_state=42
            ).generate(text_data)

            # 创建图形
            plt.figure(figsize=(10, 8))
            plt.imshow(wordcloud, interpolation='bilinear')
            plt.axis('off')
            plt.title(f'"{word}" 相关词云', fontsize=16, fontweight='bold')

            # 保存图片
            output_path = os.path.join(self.results_dir, f'{word}_wordcloud.png')
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"词云图已保存: {output_path}")

        except Exception as e:
            print(f"生成词云图失败: {e}")
    
    def save_results(self):
        """保存统计结果"""
        results = {
            "词语统计": self.word_counts,
            "总计": sum(self.word_counts.values()),
            "分析时间": str(datetime.now())
        }
        
        # 保存JSON格式结果
        json_path = os.path.join(self.results_dir, 'analysis_results.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 保存文本格式结果
        txt_path = os.path.join(self.results_dir, 'analysis_results.txt')
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write("清代西域诗集注词语统计结果\n")
            f.write("=" * 40 + "\n\n")
            
            for word, count in self.word_counts.items():
                f.write(f"'{word}' 出现次数: {count}\n")
            
            f.write(f"\n三个词语总出现次数: {sum(self.word_counts.values())}\n")
        
        print(f"统计结果已保存: {json_path}, {txt_path}")
    
    def run_analysis(self, document_path):
        """运行完整分析流程"""
        print("开始文档分析...")
        print("=" * 50)
        
        # 1. 加载文档
        if not self.load_document(document_path):
            return False
        
        # 2. 统计词语
        self.count_target_words()
        
        # 3. 生成词云
        print("\n开始生成词云图...")
        for word in self.target_words:
            if self.word_counts[word] > 0:
                text_data = self.prepare_wordcloud_text(word)
                self.generate_wordcloud(word, text_data)
            else:
                print(f"'{word}' 未出现，跳过词云生成")
        
        # 4. 保存结果
        self.save_results()
        
        print("\n分析完成！")
        print("=" * 50)
        return True

def main():
    """主函数"""
    # 检查文档文件
    doc_files = [
        "清代西域诗集注.doc",
        "清代西域诗辑注.docx", 
        "清代西域诗研究（星汉老师论著）.doc"
    ]
    
    analyzer = DocumentAnalyzer()
    
    # 尝试找到并分析文档
    for doc_file in doc_files:
        if os.path.exists(doc_file):
            print(f"找到文档文件: {doc_file}")
            if analyzer.run_analysis(doc_file):
                break
        else:
            print(f"文档文件不存在: {doc_file}")
    else:
        print("未找到任何可分析的文档文件")
        print("请确保以下文件之一存在于当前目录:")
        for doc_file in doc_files:
            print(f"  - {doc_file}")

if __name__ == "__main__":
    # 导入datetime
    from datetime import datetime
    main()
